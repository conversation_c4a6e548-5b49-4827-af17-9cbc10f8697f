import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { BellIcon } from "@heroicons/react/24/outline";

interface NotificationFloatingIconProps {
  unreadCount?: number;
}

const NotificationFloatingIcon: React.FC<NotificationFloatingIconProps> = ({
  unreadCount = 0,
}) => {
  const navigate = useNavigate();
  const [isHovered, setIsHovered] = useState(false);

  const handleClick = () => {
    navigate("/member/notifications");
  };

  return (
    <div className="fixed bottom-6 right-20 z-50">
      <button
        onClick={handleClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        className={`
          relative flex items-center justify-center w-14 h-14 
          bg-[#E63946] text-white rounded-full shadow-lg 
          hover:bg-[#d63384] transition-all duration-200 
          transform hover:scale-110 focus:outline-none focus:ring-2 
          focus:ring-[#E63946] focus:ring-offset-2
          ${isHovered ? "shadow-xl" : "shadow-lg"}
        `}
        aria-label={`Notifications${unreadCount > 0 ? ` (${unreadCount} unread)` : ""}`}
      >
        <BellIcon className="h-6 w-6" />

        {/* Unread count badge */}
        {unreadCount > 0 && (
          <span className="absolute -top-2 -right-2 bg-white text-[#E63946] text-xs font-bold rounded-full h-6 w-6 flex items-center justify-center border-2 border-[#E63946] shadow-sm">
            {unreadCount > 99 ? "99+" : unreadCount}
          </span>
        )}

        {/* Pulse animation for unread notifications */}
        {unreadCount > 0 && (
          <span className="absolute inset-0 rounded-full bg-[#E63946] animate-ping opacity-20"></span>
        )}
      </button>

      {/* Tooltip */}
      {isHovered && (
        <div className="absolute bottom-16 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-sm px-3 py-2 rounded-lg shadow-lg whitespace-nowrap">
          Notifications
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
        </div>
      )}
    </div>
  );
};

export default NotificationFloatingIcon;
